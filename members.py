import subprocess
import os
import re

def get_windows_users():
    result = subprocess.run(['net', 'user'], stdout=subprocess.PIPE, text=True, shell=True)
    lines = result.stdout.splitlines()
    users = []
    capture = False
    for line in lines:
        if '---' in line:
            capture = not capture
            continue
        if capture:
            users.extend(line.split())
    excluded_users = {
        'Administrator', 'DefaultAccount', 'Guest', 'WDAGUtilityAccount',
        'The', 'command', 'completed', 'successfully.'
    }
    users = [user for user in users if user not in excluded_users and user.isalnum()]
    return users

def run_script_as_user(script_path, username, password):
    try:
        # Verify if the script exists
        if not os.path.exists(script_path):
            print(f"Script path does not exist: {script_path}")
            return

        # Define the command to run the actual script
        command = [
            r'C:\Sysinternals\PsExec.exe',
            '-u', username,
            '-p', password,
            '-accepteula',
            '-w', os.path.dirname(script_path),
            'cmd.exe',
            '/c',
            script_path
        ]
        result = subprocess.run(command, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)

        stdout = result.stdout
        stderr = result.stderr

        # Read output from log file created by the batch script
        log_path = os.path.join(os.path.dirname(script_path), "output.log")
        if os.path.exists(log_path):
            with open(log_path, "r") as log_file:
                output = log_file.read()
                print(f"Output for {username}:\n{output}")
            # Remove the log file after reading its contents
            os.remove(log_path)
        else:
            print(f"No log file found for {username}.")

        if result.returncode != 0:
            print(f"Command failed with return code {result.returncode}")
        else:
            print(f"Script executed successfully for user {username}.")
    except subprocess.CalledProcessError as e:
        print(f"Failed to execute {script_path} as {username}: {e}")
        print("Error Output:\n", e.stderr)
    except Exception as e:
        print(f"An error occurred: {e}")

if __name__ == "__main__":
    users = get_windows_users()
    users.sort()
    password = "@Extens123456@"  # Replace with the actual password

    for user in users:
        # Handle special case for user 'grps'
        if user == 'gsuite':
            script_path = os.path.join(r'C:\Users', 'Administrator', 'Documents', 'grps', 'showmembers.bat')
        else:
            script_path = os.path.join(r'C:\Users', user, 'Documents', 'grps', 'showmembers.bat')
        print("\n")
        run_script_as_user(script_path, user, password)
        print("\n")
