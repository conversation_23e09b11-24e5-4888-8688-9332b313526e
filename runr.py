import subprocess
import os, time
from hma.turn_on import turn_on_vpn
from hma.change_ip import change_ip

def get_windows_users():
    result = subprocess.run(['net', 'user'], stdout=subprocess.PIPE, text=True, shell=True)
    lines = result.stdout.splitlines()
    users = []
    capture = False
    for line in lines:
        if '---' in line:
            capture = not capture
            continue
        if capture:
            users.extend(line.split())
    excluded_users = {
        'Administrator', 'DefaultAccount', 'Guest', 'WDAGUtilityAccount',
        'The', 'command', 'completed', 'successfully.'
    }
    users = [user for user in users if user not in excluded_users and user.isalnum()]
    return users

def run_script_as_user(script_path, username, password):
    try:
        command = [
            r'C:\Sysinternals\PsExec.exe',
            '-u', username,
            '-p', password,
            '-accepteula',
            '-w', 'C:\\Windows\\System32',
            'cmd.exe',
            '/c',
            script_path
        ]
        result = subprocess.run(command, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        
        if result.returncode == 0:
            print(f"{script_path} executed successfully as {username}")
            print(result.stdout)
        else:
            print(f"Error executing {script_path} as {username}")
            print(result.stderr)
    except subprocess.CalledProcessError as e:
        print(f"Failed to execute {script_path} as {username}: {e}")
        print("Error Output:\n", e.stderr)

def main():
    while True:
        users = get_windows_users()
        users.sort()
        password = "@Extens123456@"
        turn_on_vpn()

        for idx, user in enumerate(users):
            if user == "grps":
                script_path = os.path.join('C:\\Users', 'Administrator', 'Documents', 'grps', 'run.bat')
            else:
                script_path = os.path.join('C:\\Users', user, 'Documents', 'grps', 'run.bat')
            if not os.path.exists(script_path):
                print(f"Script not found for user {user}: {script_path}")
                continue
            print(f"Running script for user {user} ...")
            run_script_as_user(script_path, user, password)
            print(f"Script executed successfully for user {user}.")
            if idx < len(users) - 1:
                change_ip()
                print("IP address has been changed.")

        print("Waiting for 10 minutes before starting again...")
        time.sleep(600)

main()
